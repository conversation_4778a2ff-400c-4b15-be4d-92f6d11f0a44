#!/bin/bash
# Post-processing after running main.sh - Production Ready
set -e

# Create output directory if it doesn't exist
mkdir -p output

echo "🧹 Post-Processing: Clean and Enhance Contact Data"
echo "=================================================="
echo ""

# Show available CSV files
echo "📁 Available CSV files in output directory:"
find output -name "*.csv" -type f -exec basename {} \; 2>/dev/null | sort || echo "No CSV files found in output/"

echo ""
echo "📁 Available CSV files in current directory:"
find . -maxdepth 1 -name "*.csv" -type f -exec basename {} \; 2>/dev/null | sort || echo "No CSV files found in current directory"

echo ""
echo "Enter the CSV file to process (with or without path):"
echo "Examples: all_contacts.csv, output/all_contacts.csv, contacts_theater_contacts_scrape.csv"
read -r USER_INPUT

if [ -z "$USER_INPUT" ]; then
    echo "❌ No file specified. Exiting." >&2
    exit 1
fi

# Check if file exists as specified
if [ -f "$USER_INPUT" ]; then
    LATEST_CSV="$USER_INPUT"
elif [ -f "output/$USER_INPUT" ]; then
    LATEST_CSV="output/$USER_INPUT"
elif [ -f "./$USER_INPUT" ]; then
    LATEST_CSV="./$USER_INPUT"
else
    echo "❌ File not found: $USER_INPUT" >&2
    echo "Please check the filename and try again." >&2
    exit 1
fi

# Generate output filename based on input
BASENAME=$(basename "$LATEST_CSV" .csv)
OUTPUT="output/extracted_contacts_${BASENAME}.csv"

echo "🔄 Processing: $LATEST_CSV"
echo "📄 Output will be: $OUTPUT"

# Run the extraction with proper error handling
if python3 ./src/extraction.py "$LATEST_CSV" "$OUTPUT"; then
    echo "✅ Post-processing completed successfully!"
    echo "📁 Final cleaned contacts saved to: $OUTPUT"

    # Show file size for verification
    if [ -f "$OUTPUT" ]; then
        SIZE=$(du -h "$OUTPUT" | cut -f1)
        LINES=$(wc -l < "$OUTPUT" 2>/dev/null || echo "unknown")
        echo "📊 File size: $SIZE, Lines: $LINES"
    fi
else
    echo "❌ Post-processing failed. Check the error messages above." >&2
    exit 1
fi
