#!/usr/bin/env python3
"""
Gentle validation script - only removes obvious non-names, keeps most contacts.
"""

import pandas as pd
import re
import sys

def is_obvious_non_name(name):
    """Only reject VERY obvious non-names."""
    if not name or pd.isna(name):
        return True
    
    name = str(name).strip()
    if len(name) < 2:
        return True
    
    # Only reject VERY obvious non-names (exact matches or very close)
    obvious_non_names = [
        "קרא עוד", "read more", "לראש הדף", "to top", "חפשו אותנו", 
        "זמינים עבורכם", "שעות קבלה", "בין התאריכים", 
        "ליצירת קשר", "צור קשר", "דף הבית", "הצטרפו",
        "שאלות ותיאומים", "תשלום דמי", "לפרופיל האישי שלי במערכת"
    ]
    
    name_lower = name.lower().strip()
    
    # Only reject if it's an exact match or the name is mostly this phrase
    for non_name in obvious_non_names:
        if non_name == name_lower or (non_name in name_lower and len(name_lower) - len(non_name) <= 3):
            return True
    
    # Must contain at least some letters
    if not re.search(r'[א-תa-zA-Z]', name):
        return True
    
    return False

def has_any_contact_info(row):
    """Check if row has phone OR email (very lenient)."""
    phone = str(row.get('טלפון', '')).strip()
    email = str(row.get('אימייל', '')).strip()
    
    has_phone = phone and phone != 'nan' and len(phone) > 5
    has_email = email and email != 'nan' and '@' in email
    
    return has_phone or has_email

def gentle_clean_contacts(input_file, output_file):
    """Gentle cleaning - only remove obvious problems."""
    
    print(f"🧹 Gentle validation of {input_file}")
    print("=" * 50)
    
    # Read the data
    df = pd.read_csv(input_file, encoding='utf-8-sig')
    original_count = len(df)
    print(f"Original contacts: {original_count}")
    
    # Step 1: Remove only obvious non-names
    print("\n1. Removing only obvious non-names...")
    valid_names = ~df['שם'].apply(is_obvious_non_name)
    df = df[valid_names]
    after_names = len(df)
    print(f"   Removed {original_count - after_names} obvious non-names")
    print(f"   Remaining: {after_names}")
    
    # Step 2: Ensure some contact info (very lenient)
    print("\n2. Ensuring some contact information...")
    valid_contacts = df.apply(has_any_contact_info, axis=1)
    df = df[valid_contacts]
    after_contact = len(df)
    print(f"   Removed {after_names - after_contact} entries without any contact info")
    print(f"   Remaining: {after_contact}")
    
    # Step 3: Remove exact duplicates only
    print("\n3. Removing exact duplicates...")
    df = df.drop_duplicates(subset=['שם', 'טלפון'], keep='first')
    final_count = len(df)
    print(f"   Removed {after_contact - final_count} exact duplicates")
    print(f"   Final count: {final_count}")
    
    # Save cleaned data
    df.to_csv(output_file, index=False, encoding='utf-8-sig')
    
    # Summary
    print(f"\n✅ Gentle validation complete!")
    print(f"📊 Summary:")
    print(f"   Original: {original_count} contacts")
    print(f"   Final: {final_count} contacts")
    print(f"   Removed: {original_count - final_count} problematic entries")
    print(f"   Kept: {final_count/original_count*100:.1f}% of original data")
    
    # Contact info breakdown
    phone_count = df['טלפון'].notna().sum()
    email_count = df['אימייל'].notna().sum()
    both_count = (df['טלפון'].notna() & df['אימייל'].notna()).sum()
    
    print(f"\n📞 Contact Information:")
    print(f"   With phone: {phone_count}")
    print(f"   With email: {email_count}")
    print(f"   With both: {both_count}")
    
    print(f"\n📁 Saved to: {output_file}")
    
    return final_count

if __name__ == "__main__":
    input_file = sys.argv[1] if len(sys.argv) > 1 else "output/extracted_contacts_contacts_theater_only_contacts.csv"
    output_file = sys.argv[2] if len(sys.argv) > 2 else "output/gentle_cleaned_theater_contacts.csv"
    
    gentle_clean_contacts(input_file, output_file)
