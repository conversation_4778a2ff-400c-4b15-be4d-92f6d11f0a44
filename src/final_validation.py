#!/usr/bin/env python3
"""
Final validation script to ensure only real theater people with proper contact info.
Removes any non-names, validates contact information, and ensures theater relevance.
"""

import pandas as pd
import re
import sys
from pathlib import Path

def is_real_person_name(name):
    """Strict validation for real person names only."""
    if not name or pd.isna(name):
        return False
    
    name = str(name).strip()
    if len(name) < 2:
        return False
    
    non_names = [
        "קרא עוד", "read more", "בניין", "building", "מבנה", "ייעוץ", "counseling",
        "לפרופיל", "profile", "למערכת", "system", "דף הבית", "homepage", 
        "לראש הדף", "to top", "חפשו אותנו", "search for us", "זמינים עבורכם",
        "available for you", "הרשמה", "registration", "שעות קבלה", "office hours",
        "בין התאריכים", "between dates", "מידע נוסף", "additional info",
        "פרטים נוספים", "more details", "ליצירת קשר", "contact us",
        "צור קשר", "contact", "טלפון", "phone", "פקס", "fax", "אתר", "website",
        "כתובת", "address", "מיקום", "location", "הצטרפו", "join us",
        "למידה מרחוק", "distance learning", "קורס", "course", "תכנית", "program",
        "תחומי התמחות", "specialization areas", "סוציולוגיה של", "sociology of",
        "בית ספר", "school", "מכללת", "college", "אוניברסיטת", "university",
        "תיאטרון", "theater", "סטודיו", "studio", "מחלקת", "department",
        "שאלות ותיאומים", "questions and coordination", "תשלום דמי", "payment of fees"
    ]
    
    name_lower = name.lower()
    if any(non_name in name_lower for non_name in non_names):
        return False
    
    # Must contain letters
    if not re.search(r'[א-תa-zA-Z]', name):
        return False
    
    # Reject if contains numbers
    if re.search(r'\d', name):
        return False
    
    # For Hebrew names: must have at least 2 Hebrew words, each 2+ chars
    hebrew_words = re.findall(r'[א-ת]+', name)
    if hebrew_words:
        if len(hebrew_words) < 2:
            return False
        if any(len(word) < 2 for word in hebrew_words):
            return False
        # Must not be common non-name Hebrew words
        common_words = ['עוזר', 'ספרן', 'ספרנית', 'מנהל', 'מנהלת', 'רכז', 'רכזת', 'ראש']
        if any(word in hebrew_words for word in common_words):
            # Only reject if it's ONLY these words
            if len(hebrew_words) <= 2 and any(word in common_words for word in hebrew_words):
                return False
    
    # For English names: must have at least 2 English words, each 2+ chars  
    english_words = re.findall(r'[a-zA-Z]+', name)
    if english_words and not hebrew_words:
        if len(english_words) < 2:
            return False
        if any(len(word) < 2 for word in english_words):
            return False
    
    return True

def has_valid_contact_info(row):
    """Check if row has at least phone OR email."""
    phone = str(row.get('טלפון', '')).strip()
    email = str(row.get('אימייל', '')).strip()

    # Valid phone: Israeli format with at least 9 digits
    phone_digits = re.sub(r'[^\d]', '', phone) if phone else ''
    valid_phone = phone and phone != 'nan' and len(phone_digits) >= 9 and phone_digits.startswith('0')

    # Valid email: contains @ and domain
    valid_email = email and email != 'nan' and '@' in email and '.' in email.split('@')[-1] if email else False

    return valid_phone or valid_email

def is_theater_relevant(row):
    """Check if the contact is theater-relevant based on email domain or department."""
    email = str(row.get('אימייל', '')).lower()
    department = str(row.get('מחלקה', '')).lower()
    role = str(row.get('תפקיד', '')).lower()
    institution = str(row.get('עיר', '')).lower()
    
    # Theater-specific indicators
    theater_indicators = [
        'theater', 'theatre', 'תיאטרון', 'במה', 'stage', 'acting', 'משחק',
        'drama', 'דרמה', 'performing', 'arts', 'אמנות', 'beit-zvi', 'בית צבי',
        'impro', 'אימפרו', 'nissan-nativ', 'ניסן נתיב', 'studioact', 'סטודיו אקט'
    ]
    
    # Check if any theater indicators are present
    all_text = f"{email} {department} {role} {institution}"
    return any(indicator in all_text for indicator in theater_indicators)

def clean_final_contacts(input_file, output_file):
    """Final cleaning to ensure only real theater people with contact info."""
    
    print(f"🔍 Final validation of {input_file}")
    print("=" * 50)
    
    # Read the data
    df = pd.read_csv(input_file, encoding='utf-8-sig')
    original_count = len(df)
    print(f"Original contacts: {original_count}")
    
    # Step 1: Remove non-person names
    print("\n1. Removing non-person names...")
    valid_names = df['שם'].apply(is_real_person_name)
    df = df[valid_names]
    after_names = len(df)
    print(f"   Removed {original_count - after_names} non-person entries")
    print(f"   Remaining: {after_names}")
    
    # Step 2: Ensure valid contact info
    print("\n2. Ensuring valid contact information...")
    valid_contacts = df.apply(has_valid_contact_info, axis=1)
    df = df[valid_contacts]
    after_contact = len(df)
    print(f"   Removed {after_names - after_contact} entries without valid contact info")
    print(f"   Remaining: {after_contact}")
    
    # Step 3: Ensure theater relevance
    print("\n3. Ensuring theater relevance...")
    theater_relevant = df.apply(is_theater_relevant, axis=1)
    df = df[theater_relevant]
    after_theater = len(df)
    print(f"   Removed {after_contact - after_theater} non-theater entries")
    print(f"   Remaining: {after_theater}")
    
    # Step 4: Final deduplication by phone number
    print("\n4. Final deduplication...")
    df = df.drop_duplicates(subset=['טלפון'], keep='first')
    final_count = len(df)
    print(f"   Removed {after_theater - final_count} duplicate phone numbers")
    print(f"   Final count: {final_count}")
    
    # Save cleaned data
    df.to_csv(output_file, index=False, encoding='utf-8-sig')
    
    # Summary
    print(f"\n✅ Final validation complete!")
    print(f"📊 Summary:")
    print(f"   Original: {original_count} contacts")
    print(f"   Final: {final_count} verified theater contacts")
    print(f"   Removed: {original_count - final_count} invalid entries")
    print(f"   Success rate: {final_count/original_count*100:.1f}%")
    
    # Contact info breakdown
    phone_count = df['טלפון'].notna().sum()
    email_count = df['אימייל'].notna().sum()
    both_count = (df['טלפון'].notna() & df['אימייל'].notna()).sum()
    
    print(f"\n📞 Contact Information:")
    print(f"   With phone: {phone_count}")
    print(f"   With email: {email_count}")
    print(f"   With both: {both_count}")
    
    print(f"\n📁 Saved to: {output_file}")
    
    return final_count

if __name__ == "__main__":
    input_file = sys.argv[1] if len(sys.argv) > 1 else "output/extracted_contacts_contacts_theater_only_contacts.csv"
    output_file = sys.argv[2] if len(sys.argv) > 2 else "output/final_verified_theater_contacts.csv"
    
    clean_final_contacts(input_file, output_file)
