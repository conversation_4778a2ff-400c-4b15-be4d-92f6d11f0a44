#!/usr/bin/env python3
"""
Final proper cleaning - fix all the issues and ensure complete coverage.
"""

import pandas as pd
import sys

def is_definitely_not_a_person(name):
    """Identify entries that are definitely not person names."""
    if not name or pd.isna(name):
        return True
    
    name = str(name).strip()
    
    # Exact matches of obvious non-names
    obvious_non_names = [
        "בניין מכסיקו",  # Mexico Building
        "הבינלאומית הזו",  # This international
        "ייעוץ אקדמי",   # Academic counseling
        "לפרופיל האישי שלי במערכת",  # For my personal profile in system
        "קרא עוד",       # Read more
        "לראש הדף אימפרו",  # To top of Impro page
        "המחיר כולל משלוח בארץ",  # Price includes shipping in Israel
        "הארכיון הדיגיטלי בלוג",  # Digital archive blog
        "צרו קשר יצירת קשר",  # Contact us contact creation
        "יצירת קשר",     # Contact creation
        "בית ספר לאמנויות הבמה",  # School name as contact
        "סגורה עכשיו",   # Closed now
        "או במייל",      # Or by email
        "שאלות ותיאומים",  # Questions and coordination
        "אולם המופעים",  # Performance hall
        "תחומי התמחות",  # Specialization areas
        "דמי הרשמה",     # Registration fees
        "None"
    ]
    
    # Check for exact matches (case insensitive)
    name_lower = name.lower()
    for non_name in obvious_non_names:
        if non_name.lower() == name_lower:
            return True
    
    return False

def final_proper_clean(input_file, output_file):
    """Final proper cleaning with complete institution coverage."""
    
    print(f"🎯 Final proper cleaning of {input_file}")
    print("=" * 60)
    
    # Read the original scraped data
    df = pd.read_csv(input_file, encoding='utf-8-sig')
    original_count = len(df)
    print(f"Original contacts: {original_count}")
    
    # Show institution breakdown before cleaning
    print("\n📊 Institutions BEFORE cleaning:")
    institution_counts = df.groupby(df.columns[0]).size().sort_values(ascending=False)
    for institution, count in institution_counts.items():
        if institution not in ['עיר', '﻿עיר']:
            print(f"   {institution}: {count} contacts")
    
    # Remove only obvious non-names
    print("\n1. Removing obvious non-names...")
    valid_names = ~df['שם'].apply(is_definitely_not_a_person)
    df_cleaned = df[valid_names]
    after_cleaning = len(df_cleaned)
    print(f"   Removed {original_count - after_cleaning} obvious non-names")
    print(f"   Remaining: {after_cleaning}")
    
    # Show what we removed
    removed_df = df[df['שם'].apply(is_definitely_not_a_person)]
    if len(removed_df) > 0:
        print(f"\n   Removed entries:")
        for _, row in removed_df.iterrows():
            print(f"     - {row['שם']} ({row.iloc[0]})")
    
    # Ensure we have all expected institutions
    expected_institutions = [
        'אוניברסיטת חיפה', 'אוניברסיטת תל אביב', 'מכללת שנקר', 
        'מכללת הגליל המערבי', 'אימפרו', 'אקט בי7', 'בית צבי', 
        'סטודיו אקט', 'תיאטרון ניסן נתיב', 'במה'
    ]
    
    # Show institution breakdown after cleaning
    print("\n📊 Institutions AFTER cleaning:")
    institution_counts_after = df_cleaned.groupby(df_cleaned.columns[0]).size().sort_values(ascending=False)
    present_institutions = []
    for institution, count in institution_counts_after.items():
        if institution not in ['עיר', '﻿עיר']:
            print(f"   {institution}: {count} contacts")
            present_institutions.append(institution)
    
    # Check for missing institutions
    missing_institutions = set(expected_institutions) - set(present_institutions)
    if missing_institutions:
        print(f"\n⚠️ Missing institutions: {missing_institutions}")
        
        # Try to recover missing institutions from original data
        for missing in missing_institutions:
            print(f"\n🔍 Trying to recover {missing}...")
            missing_contacts = df[df.iloc[:, 0] == missing]
            if len(missing_contacts) > 0:
                # Find the best contact to add back
                best_contact = None
                best_score = -1
                for _, contact in missing_contacts.iterrows():
                    if is_definitely_not_a_person(contact['שם']):
                        continue
                    score = 0
                    if pd.notna(contact.get('אימייל', '')) and contact.get('אימייל', '') != '':
                        score += 2
                    if pd.notna(contact.get('טלפון', '')) and contact.get('טלפון', '') != '':
                        score += 1
                    if score > best_score:
                        best_score = score
                        best_contact = contact
                
                if best_contact is not None:
                    print(f"   ✅ Recovered: {best_contact['שם']}")
                    df_cleaned = pd.concat([df_cleaned, best_contact.to_frame().T], ignore_index=True)
                else:
                    print(f"   ❌ No suitable contact found for {missing}")
    
    final_count = len(df_cleaned)
    
    # Final institution count
    print(f"\n📊 FINAL Institutions:")
    final_institution_counts = df_cleaned.groupby(df_cleaned.columns[0]).size().sort_values(ascending=False)
    for institution, count in final_institution_counts.items():
        if institution not in ['עיר', '﻿עיר']:
            print(f"   {institution}: {count} contacts")
    
    # Save cleaned data
    df_cleaned.to_csv(output_file, index=False, encoding='utf-8-sig')
    
    # Summary
    print(f"\n✅ Final proper cleaning complete!")
    print(f"📊 Summary:")
    print(f"   Original: {original_count} contacts")
    print(f"   Final: {final_count} theater contacts")
    print(f"   Institutions: {len(final_institution_counts)-1}")  # -1 for header
    
    # Contact info breakdown
    phone_count = df_cleaned['טלפון'].notna().sum()
    email_count = df_cleaned['אימייל'].notna().sum()
    both_count = (df_cleaned['טלפון'].notna() & df_cleaned['אימייל'].notna()).sum()
    mobile_count = df_cleaned[df_cleaned['טלפון'].str.contains('05[0-9]', na=False)].shape[0]
    
    print(f"\n📞 Contact Information:")
    print(f"   With phone: {phone_count}")
    print(f"   With email: {email_count}")
    print(f"   With both: {both_count}")
    print(f"   Mobile numbers: {mobile_count}")
    
    print(f"\n📁 Saved to: {output_file}")
    
    return final_count

if __name__ == "__main__":
    input_file = sys.argv[1] if len(sys.argv) > 1 else "output/extracted_contacts_contacts_theater_only_contacts.csv"
    output_file = sys.argv[2] if len(sys.argv) > 2 else "output/final_proper_theater_contacts.csv"
    
    final_proper_clean(input_file, output_file)
