#!/usr/bin/env python3
"""
Targeted cleaning - manually remove specific problematic entries we can see.
"""

import pandas as pd
import sys

def is_problematic_name(name):
    """Remove specific problematic entries we identified."""
    if not name or pd.isna(name):
        return True
    
    name = str(name).strip().lower()
    
    # Specific problematic entries to remove
    problematic_names = [
        "בניין מכסיקו",  # Mexico Building
        "ייעוץ אקדמי",   # Academic counseling  
        "לראש הדף אימפרו",  # To top of Impro page
        "בית צבי",       # Institution name (when it's the contact name)
        "תחומי התמחות",  # Specialization areas
        "דמי הרשמה",     # Registration fees
        "ספרנית",        # Librarian (generic)
        "עוזר ספרן",     # Library assistant (generic)
        "בית ספר לאמנויות הבמה",  # School name as contact
        "ירושלים מנורה", # Jerusalem Menorah (location, not person)
        "תל אביב נועם",   # Tel Aviv Noam (location, not person)
        "הבינלאומית הזו", # This international (not a name)
    ]
    
    # Check for exact matches or very close matches
    for problematic in problematic_names:
        if problematic.lower() == name or problematic.lower() in name:
            return True
    
    return False

def clean_targeted_contacts(input_file, output_file):
    """Remove specific problematic entries."""
    
    print(f"🎯 Targeted cleaning of {input_file}")
    print("=" * 50)
    
    # Read the data
    df = pd.read_csv(input_file, encoding='utf-8-sig')
    original_count = len(df)
    print(f"Original contacts: {original_count}")
    
    # Remove problematic names
    print("\n1. Removing specific problematic entries...")
    valid_names = ~df['שם'].apply(is_problematic_name)
    df = df[valid_names]
    after_cleaning = len(df)
    print(f"   Removed {original_count - after_cleaning} problematic entries")
    print(f"   Remaining: {after_cleaning}")
    
    # Show what we removed
    removed_df = pd.read_csv(input_file, encoding='utf-8-sig')
    removed_names = removed_df[removed_df['שם'].apply(is_problematic_name)]['שם'].tolist()
    if removed_names:
        print(f"\n   Removed entries: {', '.join(removed_names)}")
    
    # Save cleaned data
    df.to_csv(output_file, index=False, encoding='utf-8-sig')
    
    # Summary
    print(f"\n✅ Targeted cleaning complete!")
    print(f"📊 Summary:")
    print(f"   Original: {original_count} contacts")
    print(f"   Final: {after_cleaning} high-quality theater contacts")
    print(f"   Removed: {original_count - after_cleaning} problematic entries")
    print(f"   Success rate: {after_cleaning/original_count*100:.1f}%")
    
    # Contact info breakdown
    phone_count = df['טלפון'].notna().sum()
    email_count = df['אימייל'].notna().sum()
    both_count = (df['טלפון'].notna() & df['אימייל'].notna()).sum()
    mobile_count = df[df['טלפון'].str.contains('05[0-9]', na=False)].shape[0]
    
    print(f"\n📞 Contact Information:")
    print(f"   With phone: {phone_count}")
    print(f"   With email: {email_count}")
    print(f"   With both: {both_count}")
    print(f"   Mobile numbers: {mobile_count}")
    
    print(f"\n📁 Saved to: {output_file}")
    
    return after_cleaning

if __name__ == "__main__":
    input_file = sys.argv[1] if len(sys.argv) > 1 else "output/gentle_cleaned_theater_contacts.csv"
    output_file = sys.argv[2] if len(sys.argv) > 2 else "output/final_theater_contacts.csv"
    
    clean_targeted_contacts(input_file, output_file)
