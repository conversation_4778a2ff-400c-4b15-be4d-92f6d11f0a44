#!/usr/bin/env python3
"""
Institution-preserving cleaning - ensure every institution has at least one contact.
"""

import pandas as pd
import sys

def is_problematic_but_keep_if_only_contact(name, institution, df):
    """Remove problematic names but keep if it's the only contact for an institution."""
    if not name or pd.isna(name) or str(name).strip().lower() == 'none':
        return True

    name = str(name).strip().lower()

    # Problematic names we'd like to remove
    problematic_names = [
        "קרא עוד", "לראש הדף", "המחזורים הקרוב", "המחיר כולל משלוח",
        "הארכ<PERSON><PERSON><PERSON> הדיגיטלי", "צרו קשר", "יצירת קשר", "בית ספר לאמנויות הבמה",
        "סגורה עכשיו", "או במייל", "אולם המופעים", "תחומי התמחות",
        "דמי הרשמה", "ספרנית", "עוזר ספרן"
    ]

    # Check if this name is problematic
    is_problematic = any(prob in name for prob in problematic_names)

    if not is_problematic:
        return False  # Keep it

    # If it's problematic, check if removing it would eliminate the entire institution
    institution_contacts = df[df.iloc[:, 0] == institution]

    # Count how many non-problematic contacts this institution has
    non_problematic_count = 0
    for _, contact_row in institution_contacts.iterrows():
        contact_name = str(contact_row['שם']).strip().lower()
        if contact_name != 'none' and not any(prob in contact_name for prob in problematic_names):
            non_problematic_count += 1

    # If this institution would have no contacts left, keep the least problematic one
    if non_problematic_count == 0:
        # Find the best contact to keep (prefer ones with email/phone)
        best_contact = None
        best_score = -1
        for _, contact_row in institution_contacts.iterrows():
            score = 0
            if pd.notna(contact_row.get('אימייל', '')) and contact_row.get('אימייל', '') != '':
                score += 2
            if pd.notna(contact_row.get('טלפון', '')) and contact_row.get('טלפון', '') != '':
                score += 1
            if score > best_score or (score == best_score and contact_row['שם'] == name):
                best_score = score
                best_contact = contact_row['שם']

        if best_contact == name:
            print(f"   Keeping '{name}' - best available contact for {institution}")
            return False  # Keep it

    # If institution has other good contacts, we can remove this problematic one
    return True  # Remove it

def preserve_institutions_clean(input_file, output_file):
    """Clean while preserving at least one contact per institution."""
    
    print(f"🏛️ Institution-preserving cleaning of {input_file}")
    print("=" * 50)
    
    # Read the data
    df = pd.read_csv(input_file, encoding='utf-8-sig')
    original_count = len(df)
    print(f"Original contacts: {original_count}")
    
    # Show institution breakdown before cleaning
    print("\n📊 Institutions BEFORE cleaning:")
    institution_counts = df.groupby(df.columns[0]).size().sort_values(ascending=False)
    for institution, count in institution_counts.items():
        if institution != 'עיר' and institution != '﻿עיר':
            print(f"   {institution}: {count} contacts")
    
    # Apply preservation-focused cleaning
    print("\n1. Cleaning while preserving institutions...")
    
    # Create a mask for contacts to keep
    keep_mask = []
    for idx, row in df.iterrows():
        name = row['שם']
        institution = row.iloc[0]  # First column is institution
        should_remove = is_problematic_but_keep_if_only_contact(name, institution, df)
        keep_mask.append(not should_remove)
    
    df_cleaned = df[keep_mask]
    after_cleaning = len(df_cleaned)
    print(f"   Removed {original_count - after_cleaning} problematic entries")
    print(f"   Remaining: {after_cleaning}")
    
    # Show institution breakdown after cleaning
    print("\n📊 Institutions AFTER cleaning:")
    institution_counts_after = df_cleaned.groupby(df_cleaned.columns[0]).size().sort_values(ascending=False)
    for institution, count in institution_counts_after.items():
        if institution != 'עיר' and institution != '﻿עיר':
            print(f"   {institution}: {count} contacts")
    
    # Ensure we didn't lose any institutions
    institutions_before = set(institution_counts.index) - {'עיר', '﻿עיר'}
    institutions_after = set(institution_counts_after.index) - {'עיר', '﻿עיר'}
    
    if institutions_before == institutions_after:
        print(f"\n✅ All {len(institutions_before)} institutions preserved!")
    else:
        lost_institutions = institutions_before - institutions_after
        print(f"\n⚠️ Lost institutions: {lost_institutions}")
    
    # Save cleaned data
    df_cleaned.to_csv(output_file, index=False, encoding='utf-8-sig')
    
    # Summary
    print(f"\n✅ Institution-preserving cleaning complete!")
    print(f"📊 Summary:")
    print(f"   Original: {original_count} contacts")
    print(f"   Final: {after_cleaning} theater contacts")
    print(f"   Removed: {original_count - after_cleaning} problematic entries")
    print(f"   Kept: {after_cleaning/original_count*100:.1f}% of data")
    print(f"   Institutions preserved: {len(institutions_after)}")
    
    # Contact info breakdown
    phone_count = df_cleaned['טלפון'].notna().sum()
    email_count = df_cleaned['אימייל'].notna().sum()
    both_count = (df_cleaned['טלפון'].notna() & df_cleaned['אימייל'].notna()).sum()
    mobile_count = df_cleaned[df_cleaned['טלפון'].str.contains('05[0-9]', na=False)].shape[0]
    
    print(f"\n📞 Contact Information:")
    print(f"   With phone: {phone_count}")
    print(f"   With email: {email_count}")
    print(f"   With both: {both_count}")
    print(f"   Mobile numbers: {mobile_count}")
    
    print(f"\n📁 Saved to: {output_file}")
    
    return after_cleaning

if __name__ == "__main__":
    input_file = sys.argv[1] if len(sys.argv) > 1 else "output/extracted_contacts_contacts_theater_only_contacts.csv"
    output_file = sys.argv[2] if len(sys.argv) > 2 else "output/institution_preserved_theater_contacts.csv"
    
    preserve_institutions_clean(input_file, output_file)
