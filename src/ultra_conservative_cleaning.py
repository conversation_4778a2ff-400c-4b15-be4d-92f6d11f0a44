#!/usr/bin/env python3
"""
Ultra conservative cleaning - only remove the most obvious non-names.
Keep all theater institutions represented.
"""

import pandas as pd
import sys

def is_very_obvious_non_name(name):
    """Only remove the most obvious non-names, keep everything else."""
    if not name or pd.isna(name):
        return True
    
    name = str(name).strip().lower()
    
    # Only remove VERY obvious non-names (exact matches only)
    very_obvious_non_names = [
        "לראש הדף אימפרו",  # To top of Impro page - exact match
        "בית ספר לאמנויות הבמה",  # When it's the contact name (not institution)
        "תחומי התמחות",  # Specialization areas - exact match
        "דמי הרשמה",     # Registration fees - exact match
    ]
    
    # Only remove exact matches
    for non_name in very_obvious_non_names:
        if non_name.lower() == name:
            return True
    
    return False

def ultra_conservative_clean(input_file, output_file):
    """Ultra conservative cleaning - keep almost everything."""
    
    print(f"🛡️ Ultra conservative cleaning of {input_file}")
    print("=" * 50)
    
    # Read the data
    df = pd.read_csv(input_file, encoding='utf-8-sig')
    original_count = len(df)
    print(f"Original contacts: {original_count}")
    
    # Show institution breakdown before cleaning
    print("\n📊 Institutions BEFORE cleaning:")
    institution_counts = df.groupby(df.columns[0]).size().sort_values(ascending=False)
    for institution, count in institution_counts.items():
        if institution != 'עיר' and institution != '﻿עיר':
            print(f"   {institution}: {count} contacts")
    
    # Remove only very obvious non-names
    print("\n1. Removing only very obvious non-names...")
    valid_names = ~df['שם'].apply(is_very_obvious_non_name)
    df = df[valid_names]
    after_cleaning = len(df)
    print(f"   Removed {original_count - after_cleaning} very obvious non-names")
    print(f"   Remaining: {after_cleaning}")
    
    # Show what we removed
    removed_df = pd.read_csv(input_file, encoding='utf-8-sig')
    removed_names = removed_df[removed_df['שם'].apply(is_very_obvious_non_name)]['שם'].tolist()
    if removed_names:
        print(f"\n   Removed entries: {', '.join(removed_names)}")
    
    # Show institution breakdown after cleaning
    print("\n📊 Institutions AFTER cleaning:")
    institution_counts_after = df.groupby(df.columns[0]).size().sort_values(ascending=False)
    for institution, count in institution_counts_after.items():
        if institution != 'עיר' and institution != '﻿עיר':
            print(f"   {institution}: {count} contacts")
    
    # Save cleaned data
    df.to_csv(output_file, index=False, encoding='utf-8-sig')
    
    # Summary
    print(f"\n✅ Ultra conservative cleaning complete!")
    print(f"📊 Summary:")
    print(f"   Original: {original_count} contacts")
    print(f"   Final: {after_cleaning} theater contacts")
    print(f"   Removed: {original_count - after_cleaning} obvious non-names")
    print(f"   Kept: {after_cleaning/original_count*100:.1f}% of data")
    print(f"   Institutions preserved: {len(institution_counts_after)-1}")  # -1 for header
    
    # Contact info breakdown
    phone_count = df['טלפון'].notna().sum()
    email_count = df['אימייל'].notna().sum()
    both_count = (df['טלפון'].notna() & df['אימייל'].notna()).sum()
    mobile_count = df[df['טלפון'].str.contains('05[0-9]', na=False)].shape[0]
    
    print(f"\n📞 Contact Information:")
    print(f"   With phone: {phone_count}")
    print(f"   With email: {email_count}")
    print(f"   With both: {both_count}")
    print(f"   Mobile numbers: {mobile_count}")
    
    print(f"\n📁 Saved to: {output_file}")
    
    return after_cleaning

if __name__ == "__main__":
    input_file = sys.argv[1] if len(sys.argv) > 1 else "output/gentle_cleaned_theater_contacts.csv"
    output_file = sys.argv[2] if len(sys.argv) > 2 else "output/ultra_conservative_theater_contacts.csv"
    
    ultra_conservative_clean(input_file, output_file)
